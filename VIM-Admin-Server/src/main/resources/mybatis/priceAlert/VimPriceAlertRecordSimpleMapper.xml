<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.priceAlert.mapper.VimPriceAlertRecordSimpleMapper">
    
    <resultMap type="VimPriceAlertRecordSimple" id="VimPriceAlertRecordSimpleResult">
        <result property="id"                       column="id" />
        <result property="ruleType"                 column="rule_type" />
        <result property="itemId"                   column="item_id" />
        <result property="itemName"                 column="item_name" />
        <result property="itemHashname"             column="item_hashname" />
        <result property="dataSource"               column="data_source" />
        <result property="oldPrice"                 column="old_price" />
        <result property="newPrice"                 column="new_price" />
        <result property="changeAmount"             column="change_amount" />
        <result property="changePercentage"         column="change_percentage" />
        <result property="thresholdValue"           column="threshold_value" />
        <result property="thresholdType"            column="threshold_type" />
        <result property="alertLevel"               column="alert_level" />
        <result property="alertStatus"              column="alert_status" />
        <result property="notificationStatus"       column="notification_status" />
        <result property="alertTime"                column="alert_time" />
        <result property="handleBy"                 column="handle_by" />
        <result property="handleTime"               column="handle_time" />
        <result property="handleRemark"             column="handle_remark" />
        <result property="extraInfo"                column="extra_info" />
    </resultMap>

    <sql id="selectVimPriceAlertRecordSimpleVo">
        select id, rule_type, item_id, item_name, item_hashname, data_source, 
               old_price, new_price, change_amount, change_percentage, threshold_value, 
               threshold_type, alert_level, alert_status, notification_status, 
               alert_time, handle_by, handle_time, handle_remark, extra_info
        from vim_price_alert_record_simple
    </sql>

    <select id="selectRecordList" parameterType="VimPriceAlertRecordSimple" resultMap="VimPriceAlertRecordSimpleResult">
        <include refid="selectVimPriceAlertRecordSimpleVo"/>
        <where>  
            <if test="ruleType != null and ruleType != ''"> and rule_type = #{ruleType}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemName != null and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="itemHashname != null and itemHashname != ''"> and item_hashname like concat('%', #{itemHashname}, '%')</if>
            <if test="dataSource != null and dataSource != ''"> and data_source = #{dataSource}</if>
            <if test="alertLevel != null "> and alert_level = #{alertLevel}</if>
            <if test="alertStatus != null "> and alert_status = #{alertStatus}</if>
            <if test="notificationStatus != null "> and notification_status = #{notificationStatus}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(alert_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(alert_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by alert_time desc
    </select>
    
    <select id="selectRecordById" parameterType="Long" resultMap="VimPriceAlertRecordSimpleResult">
        <include refid="selectVimPriceAlertRecordSimpleVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertRecord" parameterType="VimPriceAlertRecordSimple" useGeneratedKeys="true" keyProperty="id">
        insert into vim_price_alert_record_simple
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleType != null and ruleType != ''">rule_type,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="itemHashname != null">item_hashname,</if>
            <if test="dataSource != null and dataSource != ''">data_source,</if>
            <if test="oldPrice != null">old_price,</if>
            <if test="newPrice != null">new_price,</if>
            <if test="changeAmount != null">change_amount,</if>
            <if test="changePercentage != null">change_percentage,</if>
            <if test="thresholdValue != null">threshold_value,</if>
            <if test="thresholdType != null">threshold_type,</if>
            <if test="alertLevel != null">alert_level,</if>
            <if test="alertStatus != null">alert_status,</if>
            <if test="notificationStatus != null">notification_status,</if>
            <if test="alertTime != null">alert_time,</if>
            <if test="handleBy != null">handle_by,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="handleRemark != null">handle_remark,</if>
            <if test="extraInfo != null">extra_info,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleType != null and ruleType != ''">#{ruleType},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="itemHashname != null">#{itemHashname},</if>
            <if test="dataSource != null and dataSource != ''">#{dataSource},</if>
            <if test="oldPrice != null">#{oldPrice},</if>
            <if test="newPrice != null">#{newPrice},</if>
            <if test="changeAmount != null">#{changeAmount},</if>
            <if test="changePercentage != null">#{changePercentage},</if>
            <if test="thresholdValue != null">#{thresholdValue},</if>
            <if test="thresholdType != null">#{thresholdType},</if>
            <if test="alertLevel != null">#{alertLevel},</if>
            <if test="alertStatus != null">#{alertStatus},</if>
            <if test="notificationStatus != null">#{notificationStatus},</if>
            <if test="alertTime != null">#{alertTime},</if>
            <if test="handleBy != null">#{handleBy},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="handleRemark != null">#{handleRemark},</if>
            <if test="extraInfo != null">#{extraInfo},</if>
         </trim>
    </insert>

    <update id="updateRecord" parameterType="VimPriceAlertRecordSimple">
        update vim_price_alert_record_simple
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleType != null and ruleType != ''">rule_type = #{ruleType},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="itemHashname != null">item_hashname = #{itemHashname},</if>
            <if test="dataSource != null and dataSource != ''">data_source = #{dataSource},</if>
            <if test="oldPrice != null">old_price = #{oldPrice},</if>
            <if test="newPrice != null">new_price = #{newPrice},</if>
            <if test="changeAmount != null">change_amount = #{changeAmount},</if>
            <if test="changePercentage != null">change_percentage = #{changePercentage},</if>
            <if test="thresholdValue != null">threshold_value = #{thresholdValue},</if>
            <if test="thresholdType != null">threshold_type = #{thresholdType},</if>
            <if test="alertLevel != null">alert_level = #{alertLevel},</if>
            <if test="alertStatus != null">alert_status = #{alertStatus},</if>
            <if test="notificationStatus != null">notification_status = #{notificationStatus},</if>
            <if test="alertTime != null">alert_time = #{alertTime},</if>
            <if test="handleBy != null">handle_by = #{handleBy},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handleRemark != null">handle_remark = #{handleRemark},</if>
            <if test="extraInfo != null">extra_info = #{extraInfo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRecordByIds" parameterType="String">
        delete from vim_price_alert_record_simple where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countAllRecords" resultType="int">
        select count(*) from vim_price_alert_record_simple
    </select>

    <select id="countPendingRecords" resultType="int">
        select count(*) from vim_price_alert_record_simple where alert_status = 1
    </select>

    <select id="countTodayRecords" resultType="int">
        select count(*) from vim_price_alert_record_simple 
        where date_format(alert_time,'%Y-%m-%d') = date_format(now(),'%Y-%m-%d')
    </select>

    <select id="getRecordStatsByRuleType" resultType="java.util.Map">
        select rule_type as ruleType, count(*) as count
        from vim_price_alert_record_simple
        group by rule_type
    </select>

    <select id="getAlertTrendData" parameterType="int" resultType="java.util.Map">
        select date_format(alert_time,'%Y-%m-%d') as date, 
               count(*) as count,
               sum(case when rule_type = 'price_up' then 1 else 0 end) as upCount,
               sum(case when rule_type = 'price_down' then 1 else 0 end) as downCount
        from vim_price_alert_record_simple
        where alert_time >= date_sub(now(), interval #{days} day)
        group by date_format(alert_time,'%Y-%m-%d')
        order by date
    </select>

    <select id="getItemAlertRanking" parameterType="int" resultType="java.util.Map">
        select item_id as itemId, item_name as itemName, count(*) as alertCount
        from vim_price_alert_record_simple
        group by item_id, item_name
        order by alertCount desc
        limit #{limit}
    </select>

    <select id="getDataSourceStatistics" resultType="java.util.Map">
        select data_source as dataSource, count(*) as alertCount
        from vim_price_alert_record_simple
        where data_source is not null and data_source != ''
        group by data_source
        order by alertCount desc
    </select>

    <select id="selectRecordsByItemId" parameterType="Long" resultMap="VimPriceAlertRecordSimpleResult">
        <include refid="selectVimPriceAlertRecordSimpleVo"/>
        where item_id = #{itemId}
        order by alert_time desc
    </select>

    <select id="selectRecordsByRuleType" parameterType="String" resultMap="VimPriceAlertRecordSimpleResult">
        <include refid="selectVimPriceAlertRecordSimpleVo"/>
        where rule_type = #{ruleType}
        order by alert_time desc
    </select>

    <select id="selectRecordsByTimeRange" resultMap="VimPriceAlertRecordSimpleResult">
        <include refid="selectVimPriceAlertRecordSimpleVo"/>
        where alert_time between #{startTime} and #{endTime}
        order by alert_time desc
    </select>

    <update id="batchUpdateRecordStatus">
        update vim_price_alert_record_simple
        set alert_status = #{status},
            handle_by = #{handleBy},
            handle_time = #{handleTime}
        where id in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="cleanExpiredRecords" parameterType="int">
        delete from vim_price_alert_record_simple 
        where alert_time &lt; date_sub(now(), interval #{retentionDays} day)
    </delete>

    <select id="selectRecentRecords" parameterType="int" resultMap="VimPriceAlertRecordSimpleResult">
        <include refid="selectVimPriceAlertRecordSimpleVo"/>
        order by alert_time desc
        limit #{limit}
    </select>

    <select id="countRecordsByTimeRange" resultType="int">
        select count(*) from vim_price_alert_record_simple
        where alert_time between #{startTime} and #{endTime}
        <if test="ruleType != null and ruleType != ''">
            and rule_type = #{ruleType}
        </if>
    </select>

    <select id="getAlertLevelStatistics" resultType="java.util.Map">
        select alert_level as alertLevel, count(*) as count
        from vim_price_alert_record_simple
        group by alert_level
        order by alert_level
    </select>

    <select id="getHandleStatusStatistics" resultType="java.util.Map">
        select alert_status as alertStatus, count(*) as count
        from vim_price_alert_record_simple
        group by alert_status
        order by alert_status
    </select>

    <!-- 成本价异常商品结果映射 -->
    <resultMap type="com.ruoyi.project.priceAlert.domain.VimCostPriceAnomalyDTO" id="CostPriceAnomalyResult">
        <result property="itemId" column="item_id" />
        <result property="itemName" column="item_name" />
        <result property="itemHashname" column="item_hashname" />
        <result property="itemTag" column="item_tag" />
        <result property="itemImage" column="item_image" />
        <result property="priceCost" column="price_cost" />
        <result property="priceRecycle" column="price_recycle" />
        <result property="priceShow" column="price_show" />
        <result property="priceBuy" column="price_buy" />
        <result property="priceDifference" column="price_difference" />
        <result property="differencePercentage" column="difference_percentage" />
        <result property="boxIds" column="box_ids" />
        <result property="boxNames" column="box_names" />
        <result property="boxTypes" column="box_types" />
        <result property="boxPrices" column="box_prices" />
        <result property="boxCount" column="box_count" />
        <result property="itemSale" column="item_sale" />
        <result property="itemStock" column="item_stock" />
    </resultMap>

    <!-- 查询成本价异常商品列表 -->
    <select id="selectCostPriceAnomalyList" resultMap="CostPriceAnomalyResult">
        SELECT
            vi.id as item_id,
            vi.name as item_name,
            vi.hashname as item_hashname,
            vi.tag as item_tag,
            vi.image as item_image,
            vi.price_cost,
            vi.price_recycle,
            vi.price_show,
            vi.price_buy,
            vi.sale as item_sale,
            vi.stock as item_stock,
            (vi.price_cost - vi.price_recycle) as price_difference,
            CASE
                WHEN vi.price_recycle > 0 THEN
                    ROUND(((vi.price_cost - vi.price_recycle) / vi.price_recycle) * 100, 2)
                ELSE 0
            END as difference_percentage,
            -- 聚合盲盒信息：将同一商品的多个盲盒信息合并
            GROUP_CONCAT(DISTINCT vb.id ORDER BY vb.id SEPARATOR ',') as box_ids,
            GROUP_CONCAT(DISTINCT vb.name ORDER BY vb.id SEPARATOR ', ') as box_names,
            GROUP_CONCAT(DISTINCT vb.type ORDER BY vb.id SEPARATOR ', ') as box_types,
            GROUP_CONCAT(DISTINCT CONCAT('¥', vb.price) ORDER BY vb.id SEPARATOR ', ') as box_prices,
            COUNT(DISTINCT vb.id) as box_count
        FROM vim_item vi
        LEFT JOIN vim_box_item vbi ON vi.id = vbi.id_item
        LEFT JOIN vim_box vb ON vbi.id_box = vb.id AND vb.delete_time IS NULL
        WHERE vi.delete_time IS NULL
            AND vi.price_cost > vi.price_recycle
            <if test="itemName != null and itemName != ''">
                AND vi.name LIKE CONCAT('%', #{itemName}, '%')
            </if>
            <if test="itemTag != null and itemTag != ''">
                AND vi.tag LIKE CONCAT('%', #{itemTag}, '%')
            </if>
            <!-- 盲盒名称筛选需要特殊处理，因为使用了GROUP BY -->
            <if test="boxName != null and boxName != ''">
                AND vi.id IN (
                    SELECT DISTINCT vbi2.id_item
                    FROM vim_box_item vbi2
                    LEFT JOIN vim_box vb2 ON vbi2.id_box = vb2.id AND vb2.delete_time IS NULL
                    WHERE vb2.name LIKE CONCAT('%', #{boxName}, '%')
                )
            </if>
        GROUP BY vi.id, vi.name, vi.hashname, vi.tag, vi.image,
                 vi.price_cost, vi.price_recycle, vi.price_show, vi.price_buy,
                 vi.sale, vi.stock
        ORDER BY price_difference DESC, vi.price_cost DESC
    </select>

    <!-- 统计成本价异常商品数量 -->
    <select id="countCostPriceAnomalyItems" resultType="int">
        SELECT COUNT(DISTINCT vi.id)
        FROM vim_item vi
        WHERE vi.delete_time = 0
            AND vi.price_cost > vi.price_recycle
    </select>

</mapper>
