package com.ruoyi.project.vimBoxSys.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

import com.ruoyi.project.VimBoxItemSys.domain.VimBoxItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.vimBoxSys.mapper.VimBoxMapper;
import com.ruoyi.project.vimBoxSys.domain.VimBox;
import com.ruoyi.project.vimBoxSys.domain.VimBoxDTO;
import com.ruoyi.project.vimBoxSys.domain.VimBoxWithItemsDTO;
import com.ruoyi.project.vimBoxSys.service.IVimBoxService;
import com.ruoyi.project.VimBoxItemSys.service.IVimBoxItemService;
import com.ruoyi.project.VimBoxItemSys.domain.VimItemAndVimBoxItemDto;
import com.ruoyi.project.commoditySys.domain.VimItem;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 盲盒修改Service业务层处理
 * 
 * <AUTHOR> and 羊
 * @date 2025-03-16
 */
@Service
public class VimBoxServiceImpl implements IVimBoxService 
{
    @Autowired
    private VimBoxMapper vimBoxMapper;

    @Autowired
    private IVimBoxItemService vimBoxItemService;

    /**
     * 查询盲盒修改
     * 
     * @param id 盲盒修改主键
     * @return 盲盒修改
     */
    @Override
    public VimBox selectVimBoxById(Long id)
    {
        return vimBoxMapper.selectVimBoxById(id);
    }

    /**
     * 查询盲盒修改列表
     * 
     * @param vimBox 盲盒修改
     * @return 盲盒修改
     */
    @Override
    public List<VimBox> selectVimBoxList(VimBox vimBox)
    {
        return vimBoxMapper.selectVimBoxList(vimBox);
    }

    /**
     * 新增盲盒修改
     * 
     * @param vimBox 盲盒修改
     * @return 结果
     */
    @Override
    public int insertVimBox(VimBox vimBox)
    {
        long currentTime = System.currentTimeMillis() / 1000; // 获取当前时间戳（秒级）
        vimBox.setVimBoxcreateTime(currentTime);
        vimBox.setVimBoxupdateTime(currentTime);
        return vimBoxMapper.insertVimBox(vimBox);
    }

    /**
     * 修改盲盒修改
     * 
     * @param vimBox 盲盒修改
     * @return 结果
     */
    @Override
    public int updateVimBox(VimBox vimBox)
    {
        long currentTime = System.currentTimeMillis() / 1000; // 获取当前时间戳（秒级）
        vimBox.setVimBoxupdateTime(currentTime);
        return vimBoxMapper.updateVimBox(vimBox);
    }

    /**
     * 批量删除盲盒修改
     * 
     * @param ids 需要删除的盲盒修改主键
     * @return 结果
     */
    @Override
    public int deleteVimBoxByIds(Long[] ids)
    {
        Long deleteTime = System.currentTimeMillis() / 1000;
        return vimBoxMapper.deleteVimBoxByIds(ids, deleteTime);
    }

    /**
     * 删除盲盒修改信息
     * 
     * @param id 盲盒修改主键
     * @return 结果
     */
    @Override
    public int deleteVimBoxById(Long id)
    {
        Long deleteTime = System.currentTimeMillis() / 1000;
        return vimBoxMapper.deleteVimBoxById(id, deleteTime);
    }

    @Override
    public BigDecimal calculateReferencePrice(Long id) {
        // 查询盲盒下所有商品及其配置
        List<VimItemAndVimBoxItemDto> itemList = vimBoxItemService.selectVimItemAndVimBoxItemByBoxId(id);
        if (itemList == null || itemList.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        // 计算物品期望价值（每个物品价格乘以概率的总和）
        BigDecimal expectedValue = BigDecimal.ZERO;
        for (VimItemAndVimBoxItemDto dto : itemList) {
            VimItem item = dto.getVimItem();
            com.ruoyi.project.VimBoxItemSys.domain.VimBoxItem boxItem = dto.getVimBoxItem();
            if (item != null && item.getPriceShow() != null && boxItem != null && boxItem.getProbability() != null) {
                BigDecimal price = item.getPriceShow();
                BigDecimal probability = new BigDecimal(boxItem.getProbability()).divide(new BigDecimal("10000000"), 10, RoundingMode.HALF_UP);
                expectedValue = expectedValue.add(price.multiply(probability));
            }
        }
        
        // 参考价格 = 物品期望价值 / 0.80
        // 这里的0.80是一个业务系数，可以根据实际需求调整
        return expectedValue.divide(new BigDecimal("0.80"), 2, RoundingMode.HALF_UP);
    }
    
    /**
     * 计算盲盒返奖率
     *
     * @param id 盲盒ID
     * @return 返奖率（百分比）
     */
    @Override
    public BigDecimal calculateReturnRate(Long id) {
        // 获取盲盒信息
        VimBox vimBox = selectVimBoxById(id);
        if (vimBox == null || vimBox.getPrice() == null || vimBox.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        // 查询盲盒下所有商品及其配置
        List<VimItemAndVimBoxItemDto> itemList = vimBoxItemService.selectVimItemAndVimBoxItemByBoxId(id);
        if (itemList == null || itemList.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 计算物品期望价值（每个物品回收价格乘以概率的总和）
        BigDecimal expectedValue = BigDecimal.ZERO;
        for (VimItemAndVimBoxItemDto dto : itemList) {
            VimItem item = dto.getVimItem();
            com.ruoyi.project.VimBoxItemSys.domain.VimBoxItem boxItem = dto.getVimBoxItem();
            if (item != null && item.getPriceRecycle() != null && boxItem != null && boxItem.getProbability() != null) {
                BigDecimal price = item.getPriceRecycle(); // 修改：使用回收价格
                BigDecimal probability = new BigDecimal(boxItem.getProbability()).divide(new BigDecimal("10000000"), 10, RoundingMode.HALF_UP);
                expectedValue = expectedValue.add(price.multiply(probability));
            }
        }

        // 计算返奖率 = (物品期望价值 / 盲盒价格) * 100%
        return expectedValue.multiply(new BigDecimal("100"))
                .divide(vimBox.getPrice(), 2, RoundingMode.HALF_UP);
    }

    /**
     * 批量计算盲盒参考价格和返奖率
     * 优化版本：使用单次SQL查询获取所有数据，避免N+1查询问题
     *
     * @param boxIds 盲盒ID列表
     * @return 包含参考价格和返奖率的Map，key为盲盒ID
     */
    @Override
    public Map<Long, VimBoxDTO> batchCalculateBoxMetrics(List<Long> boxIds) {
        Map<Long, VimBoxDTO> resultMap = new HashMap<>();

        if (boxIds == null || boxIds.isEmpty()) {
            return resultMap;
        }

        // 批量查询盲盒及其商品信息
        List<VimBoxWithItemsDTO> boxWithItemsList = vimBoxMapper.selectVimBoxWithItemsByIds(boxIds);

        // 按盲盒ID分组
        Map<Long, List<VimBoxWithItemsDTO>> groupedByBoxId = boxWithItemsList.stream()
                .collect(Collectors.groupingBy(VimBoxWithItemsDTO::getBoxId));

        // 为每个盲盒计算参考价格和返奖率
        for (Map.Entry<Long, List<VimBoxWithItemsDTO>> entry : groupedByBoxId.entrySet()) {
            Long boxId = entry.getKey();
            List<VimBoxWithItemsDTO> items = entry.getValue();

            if (items.isEmpty()) {
                continue;
            }

            // 获取盲盒基本信息（从第一条记录中获取）
            VimBoxWithItemsDTO firstItem = items.get(0);
            BigDecimal boxPrice = firstItem.getBoxPrice();

            if (boxPrice == null || boxPrice.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 计算物品期望价值
            BigDecimal expectedValue = calculateExpectedValue(items);

            // 计算参考价格和返奖率
            BigDecimal referencePrice = expectedValue.divide(new BigDecimal("0.80"), 2, RoundingMode.HALF_UP);
            BigDecimal returnRate = expectedValue.multiply(new BigDecimal("100"))
                    .divide(boxPrice, 2, RoundingMode.HALF_UP);

            // 创建VimBoxDTO对象
            VimBoxDTO dto = new VimBoxDTO();
            dto.setId(boxId);
            dto.setName(firstItem.getBoxName());
            dto.setPrice(boxPrice);
            dto.setReferencePrice(referencePrice);
            dto.setReturnRate(returnRate);

            resultMap.put(boxId, dto);
        }

        return resultMap;
    }

    /**
     * 计算物品期望价值的辅助方法
     *
     * @param items 盲盒商品列表
     * @return 期望价值
     */
    private BigDecimal calculateExpectedValue(List<VimBoxWithItemsDTO> items) {
        BigDecimal expectedValue = BigDecimal.ZERO;

        for (VimBoxWithItemsDTO item : items) {
            if (item.getItemId() != null && item.getItemPriceRecycle() != null &&
                item.getProbability() != null) {

                BigDecimal price = item.getItemPriceRecycle(); // 修改：使用回收价格
                BigDecimal probability = new BigDecimal(item.getProbability())
                        .divide(new BigDecimal("10000000"), 10, RoundingMode.HALF_UP);
                expectedValue = expectedValue.add(price.multiply(probability));
            }
        }

        return expectedValue;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyBox(Long id) {
        // 查询盲盒信息
        VimBox originalBox = selectVimBoxById(id);
        if (originalBox == null) {
            throw new RuntimeException("原始盲盒不存在");
        }

        // 复制盲盒基本信息
        VimBox newBox = new VimBox();
        newBox.setVimBoxtype(originalBox.getVimBoxtype());
        newBox.setName(originalBox.getName() + "_副本");
        newBox.setImageBox(originalBox.getImageBox());
        newBox.setImageItem(originalBox.getImageItem());
        newBox.setPrice(originalBox.getPrice());
        // 复制的盲盒默认为下架状态
        newBox.setSale(0L);
        newBox.setSaleVolume(0L);
        newBox.setSaleAmount(BigDecimal.ZERO);
        newBox.setFeatured(originalBox.getFeatured());
        newBox.setLevel(originalBox.getLevel());

        // 插入新盲盒
        insertVimBox(newBox);

        //根据盲盒id 查询盲盒下的商品配置
        List<VimItemAndVimBoxItemDto> itemList = vimBoxItemService.selectVimItemAndVimBoxItemByBoxId(id);
        if (itemList == null || itemList.isEmpty()) {
            return;
        }

        // 复制商品配置
        for (VimItemAndVimBoxItemDto dto : itemList) {
            VimBoxItem boxItem = dto.getVimBoxItem();
            if (boxItem != null) {
                VimBoxItem newBoxItem = new VimBoxItem();
                newBoxItem.setIdItem(boxItem.getIdItem());
                newBoxItem.setIdBox(newBox.getId());
                newBoxItem.setProbability(boxItem.getProbability());
                newBoxItem.setLevel(boxItem.getLevel());
                vimBoxItemService.insertVimBoxItem(newBoxItem);
            }
        }
    }
}
