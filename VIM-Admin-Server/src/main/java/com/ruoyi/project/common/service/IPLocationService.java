package com.ruoyi.project.common.service;

import java.util.List;
import java.util.Map;

/**
 * IP地理位置查询Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface IPLocationService {
    
    /**
     * 查询单个IP地址的地理位置
     * 
     * @param ip IP地址
     * @return 地理位置信息
     */
    Map<String, Object> getIPLocation(String ip);
    
    /**
     * 批量查询IP地址的地理位置
     * 
     * @param ips IP地址列表
     * @return IP地址到地理位置信息的映射
     */
    Map<String, Map<String, Object>> getBatchIPLocations(List<String> ips);
}
