package com.ruoyi.project.common.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.project.common.service.IPLocationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * IP地理位置查询Service实现类
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Service
public class IPLocationServiceImpl implements IPLocationService {
    
    private static final Logger logger = LoggerFactory.getLogger(IPLocationServiceImpl.class);
    
    private final RestTemplate restTemplate = new RestTemplate();
    
    // 缓存查询结果，避免重复查询
    private final Map<String, Map<String, Object>> cache = new ConcurrentHashMap<>();
    
    // IP地址正则表达式
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );
    
    // 使用免费的IP-API.com服务
    private static final String IP_API_URL = "http://ip-api.com/json/{ip}?lang=zh-CN";
    
    @Override
    public Map<String, Object> getIPLocation(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return createDefaultLocation("IP地址为空");
        }
        
        // 去除空格
        ip = ip.trim();
        
        // 检查是否为内网IP
        if (isPrivateIP(ip)) {
            return createDefaultLocation("内网IP");
        }
        
        // 验证IP格式
        if (!IP_PATTERN.matcher(ip).matches()) {
            return createDefaultLocation("IP格式无效");
        }
        
        // 检查缓存
        if (cache.containsKey(ip)) {
            return cache.get(ip);
        }
        
        try {
            // 调用IP-API.com查询地理位置
            String url = IP_API_URL.replace("{ip}", ip);
            String response = restTemplate.getForObject(url, String.class);
            
            if (response != null) {
                JSONObject jsonObject = JSON.parseObject(response);
                
                if ("success".equals(jsonObject.getString("status"))) {
                    Map<String, Object> location = new HashMap<>();
                    location.put("ip", ip);
                    location.put("country", jsonObject.getString("country"));
                    location.put("region", jsonObject.getString("regionName"));
                    location.put("city", jsonObject.getString("city"));
                    location.put("isp", jsonObject.getString("isp"));
                    
                    // 组合地理位置信息
                    StringBuilder locationStr = new StringBuilder();
                    String country = jsonObject.getString("country");
                    String region = jsonObject.getString("regionName");
                    String city = jsonObject.getString("city");
                    
                    if (country != null && !country.isEmpty()) {
                        locationStr.append(country);
                    }
                    if (region != null && !region.isEmpty() && !region.equals(country)) {
                        if (locationStr.length() > 0) locationStr.append(" ");
                        locationStr.append(region);
                    }
                    if (city != null && !city.isEmpty() && !city.equals(region)) {
                        if (locationStr.length() > 0) locationStr.append(" ");
                        locationStr.append(city);
                    }
                    
                    location.put("location", locationStr.toString());
                    location.put("success", true);
                    
                    // 缓存结果
                    cache.put(ip, location);
                    
                    logger.info("IP地理位置查询成功: {} -> {}", ip, locationStr.toString());
                    return location;
                } else {
                    logger.warn("IP地理位置查询失败: {}, 原因: {}", ip, jsonObject.getString("message"));
                    return createDefaultLocation("查询失败");
                }
            }
        } catch (Exception e) {
            logger.error("IP地理位置查询异常: {}", ip, e);
        }
        
        return createDefaultLocation("查询异常");
    }
    
    @Override
    public Map<String, Map<String, Object>> getBatchIPLocations(List<String> ips) {
        Map<String, Map<String, Object>> results = new HashMap<>();
        
        if (ips == null || ips.isEmpty()) {
            return results;
        }
        
        for (String ip : ips) {
            if (ip != null && !ip.trim().isEmpty()) {
                results.put(ip, getIPLocation(ip));
                
                // 添加延迟，避免API限制（IP-API.com限制每分钟45次请求）
                try {
                    Thread.sleep(100); // 100ms延迟
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        return results;
    }
    
    /**
     * 创建默认地理位置信息
     */
    private Map<String, Object> createDefaultLocation(String reason) {
        Map<String, Object> location = new HashMap<>();
        location.put("ip", "");
        location.put("country", "");
        location.put("region", "");
        location.put("city", "");
        location.put("isp", "");
        location.put("location", reason);
        location.put("success", false);
        return location;
    }
    
    /**
     * 判断是否为内网IP
     */
    private boolean isPrivateIP(String ip) {
        if (ip.startsWith("192.168.") || 
            ip.startsWith("10.") || 
            ip.startsWith("172.")) {
            return true;
        }
        if (ip.startsWith("127.") || ip.equals("localhost")) {
            return true;
        }
        return false;
    }
}
