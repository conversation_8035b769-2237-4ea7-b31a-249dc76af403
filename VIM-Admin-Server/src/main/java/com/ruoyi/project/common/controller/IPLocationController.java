package com.ruoyi.project.common.controller;

import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.common.service.IPLocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * IP地理位置查询Controller
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Api(tags = "IP地理位置查询")
@RestController
@RequestMapping("/common/ip")
public class IPLocationController extends BaseController {

    @Autowired
    private IPLocationService ipLocationService;

    /**
     * 查询单个IP地址的地理位置
     */
    @ApiOperation(value = "查询IP地理位置", notes = "根据IP地址查询地理位置信息")
    @GetMapping("/location/{ip}")
    public AjaxResult getIPLocation(@PathVariable("ip") String ip) {
        try {
            Map<String, Object> location = ipLocationService.getIPLocation(ip);
            return success(location);
        } catch (Exception e) {
            logger.error("查询IP地理位置失败: {}", ip, e);
            return error("查询IP地理位置失败: " + e.getMessage());
        }
    }

    /**
     * 批量查询IP地址的地理位置
     */
    @ApiOperation(value = "批量查询IP地理位置", notes = "批量查询多个IP地址的地理位置信息")
    @PostMapping("/locations")
    public AjaxResult getBatchIPLocations(@RequestBody List<String> ips) {
        try {
            Map<String, Map<String, Object>> locations = ipLocationService.getBatchIPLocations(ips);
            return success(locations);
        } catch (Exception e) {
            logger.error("批量查询IP地理位置失败", e);
            return error("批量查询IP地理位置失败: " + e.getMessage());
        }
    }
}
