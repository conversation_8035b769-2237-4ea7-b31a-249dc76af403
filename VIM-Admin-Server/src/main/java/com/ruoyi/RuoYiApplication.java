package com.ruoyi;

import com.ruoyi.common.utils.VimUserBatchCreateUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@EnableScheduling // 启用定时任务
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class RuoYiApplication {
    public static void main(String[] args) {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        // System.out.println(new BCryptPasswordEncoder().encode("
        // NMeF5fTD)ctyRM1X^Vi"));
        ConfigurableApplicationContext context = SpringApplication.run(RuoYiApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  若依启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                " .-------.       ____     __        \n" +
                " |  _ _   \\      \\   \\   /  /    \n" +
                " | ( ' )  |       \\  _. /  '       \n" +
                " |(_ o _) /        _( )_ .'         \n" +
                " | (_,_).' __  ___(_ o _)'          \n" +
                " |  |\\ \\  |  ||   |(_,_)'         \n" +
                " |  | \\ `'   /|   `-'  /           \n" +
                " |  |  \\    /  \\      /           \n" +
                " ''-'   `'-'    `-..-'              ");

        // 批量创建测试用户工具
        testBatchCreateUsers(context);
    }

    /**
     * 测试批量创建用户功能
     * 注意：这是测试代码，正式环境请注释掉
     */
    private static void testBatchCreateUsers(ConfigurableApplicationContext context) {
        try {
            System.out.println("\n=== 开始测试批量创建VIM用户功能 ===");

            VimUserBatchCreateUtil batchCreateUtil = context.getBean(VimUserBatchCreateUtil.class);

            // 创建30个测试用户
            int createCount = 30;
            System.out.println("准备创建 " + createCount + " 个测试用户...");

            VimUserBatchCreateUtil.BatchCreateResult result = batchCreateUtil.batchCreateUsers(createCount);

            // 输出结果
            System.out.println("\n=== 批量创建结果 ===");
            System.out.println("总数: " + result.getTotalCount());
            System.out.println("成功: " + result.getSuccessCount());
            System.out.println("失败: " + result.getFailedCount());
            System.out.println("耗时: " + result.getDuration() + "ms");

            if (result.getTableFilePath() != null) {
                System.out.println("表格文件: " + result.getTableFilePath());
            }

            if (result.getSuccessCount() > 0) {
                System.out.println("\n成功创建的用户昵称:");
                result.getSuccessUsers().forEach(nickname -> System.out.println("  - " + nickname));

                System.out.println("\n用户账号密码信息:");
                if (result.getUserAccountInfos() != null) {
                    result.getUserAccountInfos().forEach(info ->
                        System.out.printf("  ID:%d | 用户名:%s | 昵称:%s | 手机:%s | 密码:%s%n",
                            info.getUserId(), info.getUsername(), info.getNickname(),
                            info.getPhone(), info.getPassword())
                    );
                }
            }

            if (result.getFailedCount() > 0) {
                System.out.println("\n失败的用户:");
                result.getFailedUsers().forEach(error -> System.out.println("  - " + error));
            }

            System.out.println("\n=== 测试完成 ===");
            System.out.println("注意：测试用户ID范围为1-199，正式用户ID从200开始");
            System.out.println("用户账号密码已保存到CSV文件，可用Excel打开查看");

        } catch (Exception e) {
            System.err.println("批量创建用户测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
