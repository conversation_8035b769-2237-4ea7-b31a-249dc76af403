/**
 * IP地址工具类
 * 提供IP地址转换为地理位置的功能
 */
import { getBatchIPLocations, getIPLocation } from '@/api/common/ipLocation'

// IP地址缓存，避免重复请求
const ipCache = new Map();

/**
 * 将IP地址转换为地理位置信息
 * @param {string} ip - IP地址
 * @returns {Promise<object>} - 地理位置信息对象
 */
export function ipToLocation(ip) {
  // 如果IP为空或无效，返回默认值
  if (!ip || ip === 'unknown' || ip === '127.0.0.1' || ip.startsWith('192.168.') || ip.startsWith('10.')) {
    return Promise.resolve({
      city: '本地',
      region: '本地',
      country: '本地',
      location: '内网IP',
      isp: '内网',
      success: false
    });
  }

  // 检查缓存中是否已有该IP的地理位置信息
  if (ipCache.has(ip)) {
    return Promise.resolve(ipCache.get(ip));
  }

  // 使用后端API查询IP地理位置
  return getIPLocation(ip)
    .then(response => {
      if (response.code === 200 && response.data) {
        const data = response.data;
        const locationInfo = {
          city: data.city || '未知',
          region: data.region || '未知',
          country: data.country || '未知',
          location: data.location || '未知位置',
          isp: data.isp || '未知',
          success: data.success || false
        };

        // 将结果存入缓存
        ipCache.set(ip, locationInfo);

        return locationInfo;
      } else {
        throw new Error(`IP地址查询失败: ${response.msg || '未知错误'}`);
      }
    })
    .catch(error => {
      console.warn('IP地理位置查询失败:', ip, error);

      // 发生错误时，返回默认信息
      const locationInfo = {
        city: '未知',
        region: '未知',
        country: '未知',
        location: '查询失败',
        isp: '未知',
        success: false
      };

      // 将结果存入缓存，避免重复查询失败的IP
      ipCache.set(ip, locationInfo);

      return locationInfo;
    });
}

/**
 * 批量转换多个IP地址为地理位置
 * @param {Array<string>} ipList - IP地址列表
 * @returns {Promise<Map>} - IP地址到地理位置的映射
 */
export function batchIpToLocation(ipList) {
  if (!ipList || ipList.length === 0) {
    return Promise.resolve(new Map());
  }

  const uniqueIps = [...new Set(ipList.filter(ip => ip && ip.trim()))]; // 去重并过滤空值

  // 检查缓存，分离已缓存和未缓存的IP
  const cachedResults = new Map();
  const uncachedIps = [];

  uniqueIps.forEach(ip => {
    if (ipCache.has(ip)) {
      cachedResults.set(ip, ipCache.get(ip));
    } else {
      uncachedIps.push(ip);
    }
  });

  // 如果所有IP都已缓存，直接返回
  if (uncachedIps.length === 0) {
    return Promise.resolve(cachedResults);
  }

  // 使用后端批量查询API
  return getBatchIPLocations(uncachedIps)
    .then(response => {
      if (response.code === 200 && response.data) {
        const batchResults = response.data;

        // 处理批量查询结果
        Object.keys(batchResults).forEach(ip => {
          const data = batchResults[ip];
          const locationInfo = {
            city: data.city || '未知',
            region: data.region || '未知',
            country: data.country || '未知',
            location: data.location || '未知位置',
            isp: data.isp || '未知',
            success: data.success || false
          };

          // 缓存结果
          ipCache.set(ip, locationInfo);
          cachedResults.set(ip, locationInfo);
        });
      }

      return cachedResults;
    })
    .catch(error => {
      console.warn('批量IP地理位置查询失败:', error);

      // 查询失败时，为未缓存的IP设置默认值
      uncachedIps.forEach(ip => {
        const defaultInfo = {
          city: '未知',
          region: '未知',
          country: '未知',
          location: '查询失败',
          isp: '未知',
          success: false
        };
        ipCache.set(ip, defaultInfo);
        cachedResults.set(ip, defaultInfo);
      });

      return cachedResults;
    });
}

/**
 * 清除IP地址缓存
 */
export function clearIpCache() {
  ipCache.clear();
} 