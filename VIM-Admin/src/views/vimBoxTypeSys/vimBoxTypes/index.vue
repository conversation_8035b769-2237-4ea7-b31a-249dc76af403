<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" @submit.native.prevent>
      <el-form-item label="盲盒分类" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入盲盒分类"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="对战系列" prop="battle">
        <el-select v-model="queryParams.battle" placeholder="请选择对战系列" clearable>
          <el-option label="否" :value="0" />
          <el-option label="是" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="可开箱" prop="open">
        <el-select v-model="queryParams.open" placeholder="请选择开箱状态" clearable>
          <el-option label="不可开箱" :value="0" />
          <el-option label="可开箱" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['vimBoxTypeSys:vimBoxTypes:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['vimBoxTypeSys:vimBoxTypes:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['vimBoxTypeSys:vimBoxTypes:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['vimBoxTypeSys:vimBoxTypes:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="vimBoxTypesList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="盲盒分类id" align="center" prop="id" width="100" />
      <el-table-column label="盲盒分类" align="center" prop="name" width="160" />
      <el-table-column label="优先级" align="center" prop="sort" width="100" />
      <el-table-column label="对战系列" align="center" prop="battle" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.battle === 1 ? 'danger' : 'info'" size="small">
            {{ scope.row.battle === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="可开箱" align="center" prop="open" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.open === 1 ? 'success' : 'warning'" size="small">
            {{ scope.row.open === 1 ? '可开箱' : '不可开箱' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="vimBoxTypecreateTime">
        <template #default="scope">
          {{ formatDate(scope.row.vimBoxTypecreateTime) }}
        </template>
      </el-table-column>

      <el-table-column label="修改时间" align="center" prop="vimBoxTypeupdateTime">
        <template #default="scope">
          {{ formatDate(scope.row.vimBoxTypeupdateTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['vimBoxTypeSys:vimBoxTypes:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['vimBoxTypeSys:vimBoxTypes:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改盲盒分类对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="vimBoxTypesRef" :model="form" :rules="rules" label-width="80px">
        <!-- 隐藏的时间戳字段 -->
        <el-form-item v-if="form.id === null" prop="vimBoxTypecreateTime" style="display: none">
          <el-input v-model="form.vimBoxTypecreateTime" type="hidden" />
        </el-form-item>
        <el-form-item v-else prop="vimBoxTypeupdateTime" style="display: none">
          <el-input v-model="form.vimBoxTypeupdateTime" type="hidden" />
        </el-form-item>
        <el-form-item label="盲盒分类" prop="name">
          <el-input v-model="form.name" placeholder="请输入盲盒分类" />
        </el-form-item>
        <el-form-item label="优先级" prop="sort">
          <el-input v-model="form.sort" placeholder="请输入优先级" />
        </el-form-item>
        <el-form-item label="对战系列" prop="battle">
          <el-select v-model="form.battle" placeholder="请选择对战系列" clearable style="width: 100%">
            <el-option label="否" :value="0" />
            <el-option label="是" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="可开箱" prop="open">
          <el-select v-model="form.open" placeholder="请选择开箱状态" clearable style="width: 100%">
            <el-option label="不可开箱" :value="0" />
            <el-option label="可开箱" :value="1" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VimBoxTypes">
import { listVimBoxTypes, getVimBoxTypes, delVimBoxTypes, addVimBoxTypes, updateVimBoxTypes } from "@/api/vimBoxTypeSys/vimBoxTypes";

const { proxy } = getCurrentInstance();

const vimBoxTypesList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 时间戳格式化函数
const formatDate = (timestamp) => {
  if (!timestamp) return "无";
  // 假设时间戳是秒级（如 1741960435），需转为毫秒
  const date = new Date(timestamp * 1000);
  // 返回格式：YYYY-MM-DD HH:mm:ss
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    battle: null,
    open: null,
  },
  rules: {
    name: [
      { required: true, message: "盲盒分类不能为空", trigger: "blur" }
    ],
    sort: [
      { required: true, message: "优先级不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询盲盒分类列表 */
function getList() {
  loading.value = true;
  listVimBoxTypes(queryParams.value).then(response => {
    console.log("API 返回的数据：", response.rows);
    vimBoxTypesList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    sort: null,
    battle: 0, // 默认为否
    open: 1,   // 默认为可开箱
    vimBoxTypecreateTime: null,
    vimBoxTypeupdateTime: null,
    vimBoxTypedeleteTime: null
  };
  proxy.resetForm("vimBoxTypesRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  console.log("搜索参数：", queryParams.value);
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  // 新增时设置默认值
  form.value.sort = 1;
  form.value.battle = 0; // 默认为否
  form.value.open = 1;   // 默认为可开箱
  form.value.vimBoxTypecreateTime = Math.floor(Date.now() / 1000);
  open.value = true;
  title.value = "添加盲盒分类";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getVimBoxTypes(_id).then(response => {
    console.log("获取的数据：", response.data);
    form.value = response.data;

    // 确保battle和open字段是数字类型
    if (form.value.battle !== null && form.value.battle !== undefined) {
      form.value.battle = Number(form.value.battle);
    }
    if (form.value.open !== null && form.value.open !== undefined) {
      form.value.open = Number(form.value.open);
    }

    form.value.vimBoxTypeupdateTime = Math.floor(Date.now() / 1000);
    console.log("处理后的表单数据：", form.value);
    open.value = true;
    title.value = "修改盲盒分类";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["vimBoxTypesRef"].validate(valid => {
    if (valid) {
      // 确保数据类型正确
      const submitData = { ...form.value };

      // 确保battle和open字段是数字类型
      if (submitData.battle !== null && submitData.battle !== undefined) {
        submitData.battle = Number(submitData.battle);
      }
      if (submitData.open !== null && submitData.open !== undefined) {
        submitData.open = Number(submitData.open);
      }

      console.log("提交的数据：", submitData);

      if (submitData.id != null) {
        updateVimBoxTypes(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        }).catch(error => {
          console.error("修改失败：", error);
          proxy.$modal.msgError("修改失败：" + (error.msg || error.message || "未知错误"));
        });
      } else {
        addVimBoxTypes(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        }).catch(error => {
          console.error("新增失败：", error);
          proxy.$modal.msgError("新增失败：" + (error.msg || error.message || "未知错误"));
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除盲盒分类编号为"' + _ids + '"的数据项？').then(function() {
    return delVimBoxTypes(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('vimBoxTypeSys/vimBoxTypes/export', {
    ...queryParams.value
  }, `vimBoxTypes_${new Date().getTime()}.xlsx`)
}

getList();
</script>
