/**
 * 仪表盘数据处理组合式函数
 * 提供数据获取和处理的通用方法
 */
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getTotalBoxOpenings,
  getTotalSales,
  getActiveUsers,
  getAverageReturnRate,
  getTotalClaimAmount,
  getTotalRechargeAmount,
  getProfitAmount,
  getBoxTrend,
  getBoxDistribution,
  getLatestBoxRecords,
  getHotBoxes,
  getBoxPrizeStats,
  getTotalShippingCost,
  getTotalRechargeCount,
  getStatisticsReport
} from '@/api/dashboard'

/**
 * 仪表盘数据处理组合式函数
 * @param {Object} options - 配置选项
 * @param {import('vue').ComputedRef<Object>} options.dateParams - 日期参数
 * @param {import('vue').Ref<string>} options.timeRange - 时间范围
 * @param {import('vue').Ref<string>} options.streamerFilter - 主播筛选条件
 * @param {import('vue').Ref<Array>} options.dateTimeRange - 精确时间范围
 * @returns {Object} 数据相关的状态和方法
 */
export function useDashboardData({
  dateParams,
  timeRange,
  streamerFilter,
  dateTimeRange
}) {
  // 加载状态
  const loading = reactive({
    stats: false,
    trend: false,
    distribution: false,
    latestRecords: false,
    hotBoxes: false
  })

  // 数据状态 - 更新为匹配后端返回的所有数据项
  const statCards = ref([
    // 用户相关统计
    {
      label: '用户访问数',
      value: '0',
      icon: 'User',
      color: '#67C23A',
      category: '用户相关统计'
    },
    {
      label: '用户充值金额',
      value: '¥0',
      icon: 'Money',
      color: '#E6A23C',
      category: '用户相关统计'
    },
    {
      label: '用户充值笔数',
      value: '0',
      icon: 'Document',
      color: '#9C27B0',
      category: '用户相关统计'
    },
    {
      label: '用户开箱次数',
      value: '0',
      icon: 'Present',
      color: '#409EFF',
      category: '用户相关统计'
    },
    {
      label: '用户获奖金额',
      value: '¥0',
      icon: 'Present',
      color: '#8E44AD',
      category: '用户相关统计'
    },
    {
      label: '用户待发货金额',
      value: '¥0',
      icon: 'Box',
      color: '#19CAAD',
      category: '用户相关统计'
    },
    {
      label: '用户利润金额',
      value: '¥0',
      icon: 'Wallet',
      color: '#6A5ACD',
      category: '用户相关统计',
      hidden: true // 隐藏此卡片
    },
    {
      label: '用户总流水',
      value: '¥0',
      icon: 'Money',
      color: '#E6A23C',
      category: '用户相关统计'
    },
    {
      label: '用户返奖率',
      value: '0%',
      icon: 'Money',
      color: '#F56C6C',
      category: '用户相关统计'
    },

    // 主播相关统计
    {
      label: '主播访问数',
      value: '0',
      icon: 'User',
      color: '#67C23A',
      category: '主播相关统计'
    },
    {
      label: '主播充值金额',
      value: '¥0',
      icon: 'Money',
      color: '#E6A23C',
      category: '主播相关统计'
    },
    {
      label: '主播充值笔数',
      value: '0',
      icon: 'Document',
      color: '#9C27B0',
      category: '主播相关统计'
    },
    {
      label: '主播开箱次数',
      value: '0',
      icon: 'Present',
      color: '#409EFF',
      category: '主播相关统计'
    },
    {
      label: '主播获奖金额',
      value: '¥0',
      icon: 'Present',
      color: '#8E44AD',
      category: '主播相关统计'
    },
    {
      label: '主播待发货金额',
      value: '¥0',
      icon: 'Box',
      color: '#19CAAD',
      category: '主播相关统计'
    },
    {
      label: '主播利润金额',
      value: '¥0',
      icon: 'Wallet',
      color: '#6A5ACD',
      category: '主播相关统计',
      hidden: true // 隐藏此卡片
    },
    {
      label: '主播总流水',
      value: '¥0',
      icon: 'Money',
      color: '#E6A23C',
      category: '主播相关统计'
    },
    {
      label: '主播返奖率',
      value: '0%',
      icon: 'Money',
      color: '#F56C6C',
      category: '主播相关统计'
    },
    
    // 合计统计
    {
      label: '合计访问数',
      value: '0',
      icon: 'User',
      color: '#67C23A',
      category: '合计统计'
    },
    {
      label: '合计充值金额',
      value: '¥0',
      icon: 'Money',
      color: '#E6A23C',
      category: '合计统计'
    },
    {
      label: '合计充值笔数',
      value: '0',
      icon: 'Document',
      color: '#9C27B0',
      category: '合计统计'
    },
    {
      label: '合计开箱次数',
      value: '0',
      icon: 'Present',
      color: '#409EFF',
      category: '合计统计'
    },
    {
      label: '合计获奖金额',
      value: '¥0',
      icon: 'Present',
      color: '#8E44AD',
      category: '合计统计'
    },
    {
      label: '合计待发货金额',
      value: '¥0',
      icon: 'Box',
      color: '#19CAAD',
      category: '合计统计'
    },
    {
      label: '合计总流水',
      value: '¥0',
      icon: 'Money',
      color: '#E6A23C',
      category: '合计统计'
    },
    {
      label: '合计返奖率',
      value: '0%',
      icon: 'Money',
      color: '#F56C6C',
      category: '合计统计'
    },
    {
      label: '总流水',
      value: '¥0',
      icon: 'Money',
      color: '#E6A23C',
      category: '合计统计',
      hidden: true // 隐藏重复的总流水卡片
    },
    {
      label: '平均返奖率',
      value: '0%',
      icon: 'Money',
      color: '#F56C6C',
      category: '合计统计',
      hidden: true // 隐藏重复的平均返奖率卡片
    },
    
    // 其他业务统计
    {
      label: '开市电能余额',
      value: '¥0',
      icon: 'Lightning',
      color: '#F08080',
      category: '其他业务统计',
      hidden: true // 隐藏此卡片
    },
    {
      label: '开市钥匙余额',
      value: '¥0',
      icon: 'Key',
      color: '#FFD700',
      category: '其他业务统计',
      hidden: true // 隐藏此卡片
    },
    {
      label: '开市奖品余额',
      value: '¥0',
      icon: 'Present',
      color: '#8E44AD',
      category: '其他业务统计',
      hidden: true // 隐藏此卡片
    },
    {
      label: '赠送钥匙数',
      value: '0',
      icon: 'Key',
      color: '#FFD700',
      category: '其他业务统计'
    },
    {
      label: '赠送奖品金额',
      value: '¥0',
      icon: 'Present',
      color: '#8E44AD',
      category: '其他业务统计'
    },
    {
      label: '填报发货金额',
      value: '¥0',
      icon: 'Van',
      color: '#4ECDC4',
      category: '其他业务统计',
      hidden: true // 隐藏此卡片
    },
    {
      label: '实际发货金额',
      value: '¥0',
      icon: 'Van',
      color: '#4ECDC4',
      category: '其他业务统计'
    },
    {
      label: '实际利润金额',
      value: '¥0',
      icon: 'Wallet',
      color: '#6A5ACD',
      category: '其他业务统计'
    }
  ])

  // 统计数据缓存
  const statisticsCache = ref({
    data: null,
    timestamp: null,
    params: null
  })

  // 最新开箱记录
  const latestRecords = ref([])

  // 热门盲盒
  const topBoxes = ref([])

  /**
   * 构建查询参数（使用毫秒时间戳）
   * @returns {Object} 包含时间戳和筛选条件的参数对象
   */
  const buildEnhancedParams = () => {
    const params = {}

    // 添加主播筛选条件
    if (streamerFilter && streamerFilter.value) {
      params.streamerFilter = streamerFilter.value
    }

    // 构建时间参数
    if (dateTimeRange && dateTimeRange.value && dateTimeRange.value.length === 2) {
      // 直接使用精确时间范围的毫秒时间戳
      params.startDateTime = new Date(dateTimeRange.value[0]).getTime()
      params.endDateTime = new Date(dateTimeRange.value[1]).getTime()
    } else if (dateParams && dateParams.value) {
      // 将日期参数转换为毫秒时间戳
      const startDate = new Date(dateParams.value.startDate)
      startDate.setHours(0, 0, 0, 0)
      const endDate = new Date(dateParams.value.endDate)
      endDate.setHours(23, 59, 59, 999)

      params.startDateTime = startDate.getTime()
      params.endDateTime = endDate.getTime()
    } else {
      // 如果没有设置时间范围，使用当天作为默认范围
      const today = new Date()
      const startOfDay = new Date(today)
      startOfDay.setHours(0, 0, 0, 0)
      const endOfDay = new Date(today)
      endOfDay.setHours(23, 59, 59, 999)

      params.startDateTime = startOfDay.getTime()
      params.endDateTime = endOfDay.getTime()
    }

    return params
  }

  /**
   * 通用的API请求处理函数
   * @param {Function} apiCall - API调用函数
   * @param {Object} params - 请求参数
   * @param {string} errorMessage - 错误消息
   * @returns {Promise<Object|null>} 请求结果或null
   */
  const fetchData = async (apiCall, params, errorMessage) => {
    try {
      const res = await apiCall(params)
      if (res.code === 200) {
        return res.data
      } else {
        ElMessage.error(res.msg || errorMessage)
        return null
      }
    } catch (error) {
      console.error(errorMessage, error)
      ElMessage.error(errorMessage)
      return null
    }
  }

  /**
   * 根据主播筛选条件过滤统计数据
   * @param {Array} statisticsData - 完整的统计数据
   * @param {string} filter - 筛选条件 (all/exclude/only)
   * @returns {Array} 过滤后的统计数据
   */
  const filterStatisticsByStreamer = (statisticsData, filter) => {
    if (!statisticsData || !Array.isArray(statisticsData)) {
      return []
    }

    // 根据筛选条件决定显示哪些数据
    switch (filter) {
      case 'exclude':
        // 排除主播：只显示用户数据和其他业务统计
        return statisticsData.filter(item =>
          item.category === '用户相关统计' || 
          item.category === '其他业务统计' ||
          // 特殊处理：总是包含总流水和返奖率
          (item.stat_name && (item.stat_name.includes('总流水') || item.stat_name.includes('返奖率')))
        )
      case 'only':
        // 仅主播：只显示主播数据和其他业务统计
        return statisticsData.filter(item =>
          item.category === '主播相关统计' || 
          item.category === '其他业务统计' ||
          // 特殊处理：总是包含总流水和返奖率
          (item.stat_name && (item.stat_name.includes('总流水') || item.stat_name.includes('返奖率')))
        )
      case 'all':
      default:
        // 全部：显示所有数据
        return statisticsData
    }
  }

  /**
   * 将统计数据映射到statCards数组（根据筛选条件显示不同数据）
   * @param {Array} statisticsData - 统计数据数组，格式：{category, stat_name, stat_value, unit}
   * @param {string} streamerFilterValue - 主播筛选条件
   */
  const mapStatisticsToCards = (statisticsData, streamerFilterValue = 'all') => {
    if (!statisticsData || !Array.isArray(statisticsData)) {
      console.warn('统计数据格式不正确:', statisticsData)
      return
    }

    console.log('映射统计数据到卡片 - 筛选条件:', streamerFilterValue)
    console.log('原始统计数据:', statisticsData)

    // 先重置所有卡片的值
    statCards.value.forEach(card => {
      if (card.value.includes('¥')) {
        card.value = '¥0'
      } else if (card.value.includes('%')) {
        card.value = '0%'
      } else {
        card.value = '0'
      }
    })

    // 根据筛选条件过滤数据
    const filteredData = filterStatisticsByStreamer(statisticsData, streamerFilterValue)
    console.log('筛选后的数据:', filteredData)

    // 按分组整理数据
    const groupedData = {
      '用户相关统计': [],
      '主播相关统计': [],
      '合计统计': [],
      '其他业务统计': []
    }

    // 将数据按category分组
    filteredData.forEach(item => {
      if (groupedData[item.category]) {
        groupedData[item.category].push(item)
      }
    })

    console.log('分组后的数据:', groupedData)

    // 遍历所有统计数据，更新对应的卡片
    filteredData.forEach(item => {
      const { stat_name, stat_value, unit } = item
      
      // 忽略"详情"类型的数据，这些数据可能用于其他展示，不在卡片中显示
      if (stat_name && stat_name.includes('详情')) {
        console.log(`跳过详情数据: ${stat_name}`)
        return
      }
      
      // 特殊处理：根据数据来源分配利润金额
      if (stat_name === '利润金额') {
        if (item.category === '用户相关统计') {
          // 更新用户利润金额卡片
          updateCardValue('用户利润金额', stat_value, unit)
        } else if (item.category === '主播相关统计') {
          // 更新主播利润金额卡片
          updateCardValue('主播利润金额', stat_value, unit)
        } else {
          // 更新合计利润金额卡片
          updateCardValue('利润金额', stat_value, unit)
        }
        return
      }
      
      // 处理返奖率数据
      if (stat_name && stat_name.includes('返奖率')) {
        const prefix = stat_name.replace('返奖率', '').trim()
        const cardLabel = prefix ? `${prefix}返奖率` : '合计返奖率'
        updateCardValue(cardLabel, stat_value, unit || '%')
        return
      }
      
      // 处理总流水数据
      if (stat_name && stat_name.includes('总流水')) {
        const prefix = stat_name.replace('总流水', '').trim()
        const cardLabel = prefix ? `${prefix}总流水` : '合计总流水'
        updateCardValue(cardLabel, stat_value, unit || '¥')
        return
      }
      
      // 查找匹配的卡片
      const matchingCard = statCards.value.find(card => 
        card.label === stat_name || 
        (card.label.toLowerCase().includes(stat_name.toLowerCase()) && 
         card.category === item.category)
      )
      
      if (matchingCard) {
        updateCardValue(matchingCard.label, stat_value, unit)
      } else {
        console.log(`未找到匹配的卡片: ${stat_name} (${item.category})`)
      }
    })
    
    // 根据筛选条件决定显示哪些卡片
    let visibleCategories = []
    switch (streamerFilterValue) {
      case 'exclude':
        visibleCategories = ['用户相关统计', '其他业务统计']
        break
      case 'only':
        visibleCategories = ['主播相关统计', '其他业务统计']
        break
      case 'all':
      default:
        visibleCategories = ['合计统计', '其他业务统计']
        break
    }
    
    // 过滤出要显示的卡片
    const visibleCards = statCards.value.filter(card => 
      visibleCategories.includes(card.category) && !card.hidden
    )
    
    console.log('可见的卡片类别:', visibleCategories)
    console.log('可见的卡片数量:', visibleCards.length)
  }
  
  /**
   * 更新卡片值的辅助函数
   * @param {string} cardLabel - 卡片标签
   * @param {any} value - 要设置的值
   * @param {string} unit - 单位
   */
  const updateCardValue = (cardLabel, value, unit) => {
    const card = statCards.value.find(c => c.label === cardLabel)
    if (!card) {
      console.warn(`未找到卡片: ${cardLabel}`)
      return
    }
    
    let formattedValue = value || 0
    
    // 处理不同类型的值格式化
    if (unit === '元' || unit === '¥' || cardLabel.includes('金额') || cardLabel.includes('流水')) {
      // 货币格式
      formattedValue = `¥${Number(formattedValue).toLocaleString()}`
    } else if (unit === '%' || cardLabel.includes('返奖率') || cardLabel.includes('比率')) {
      // 百分比格式
      formattedValue = `${formattedValue}%`
    } else if (cardLabel.includes('次数') || cardLabel.includes('笔数') || cardLabel.includes('数量') || cardLabel.includes('访问数')) {
      // 数量格式
      formattedValue = Number(formattedValue).toLocaleString()
    } else {
      // 默认格式
      formattedValue = Number(formattedValue).toString()
    }
    
    console.log(`更新卡片 (${cardLabel}): ${formattedValue}`)
    card.value = formattedValue
  }

  /**
   * 检查缓存是否有效
   * @param {Object} currentParams - 当前查询参数
   * @returns {boolean} 缓存是否有效
   */
  const isCacheValid = (currentParams) => {
    if (!statisticsCache.value.data || !statisticsCache.value.params) {
      return false
    }

    // 检查时间参数是否相同
    const cachedParams = statisticsCache.value.params
    return (
      cachedParams.startDateTime === currentParams.startDateTime &&
      cachedParams.endDateTime === currentParams.endDateTime
    )
  }

  /**
   * 获取统计报告数据（带缓存和前端过滤）
   * @param {boolean} forceRefresh - 是否强制刷新缓存
   */
  const fetchStatisticsReport = async (forceRefresh = false) => {
    const params = buildEnhancedParams()

    // 检查缓存
    if (!forceRefresh && isCacheValid(params)) {
      console.log('使用缓存的统计数据')
      mapStatisticsToCards(statisticsCache.value.data, streamerFilter.value)
      return
    }

    console.log('从服务器获取统计数据')
    const data = await fetchData(
      getStatisticsReport,
      params,
      '获取统计报告失败'
    )

    if (data !== null) {
      // 更新缓存
      statisticsCache.value = {
        data: data,
        timestamp: Date.now(),
        params: params
      }

      // 映射数据到卡片
      mapStatisticsToCards(data, streamerFilter.value)
    }
  }

  /**
   * 当主播筛选条件改变时，使用缓存数据重新过滤
   */
  const refreshStatisticsWithFilter = () => {
    if (statisticsCache.value.data) {
      console.log('使用缓存数据重新过滤:', streamerFilter.value)
      // 使用当前的筛选条件重新映射数据
      mapStatisticsToCards(statisticsCache.value.data, streamerFilter.value)
      return true
    } else {
      // 如果没有缓存数据，重新获取
      console.log('没有缓存数据，重新获取')
      fetchStatisticsReport(true)
      return false
    }
  }

  /**
   * 获取统计卡片数据 - 总开箱次数（保留作为备用）
   */
  const fetchTotalBoxOpenings = async () => {
    const params = buildEnhancedParams()
    const data = await fetchData(
      getTotalBoxOpenings,
      params,
      '获取总开箱次数失败'
    )

    if (data !== null) {
      statCards.value[0].value = data.toString()
    }
  }

  /**
   * 获取统计卡片数据 - 活跃用户数
   */
  const fetchActiveUsers = async () => {
    const params = buildEnhancedParams()
    const data = await fetchData(
      getActiveUsers,
      { days: 30, ...params },
      '获取活跃用户数失败'
    )

    if (data !== null) {
      statCards.value[1].value = data.toString()
    }
  }

  /**
   * 获取统计卡片数据 - 总销售额
   */
  const fetchTotalSales = async () => {
    const params = buildEnhancedParams()
    const data = await fetchData(
      getTotalSales,
      params,
      '获取总销售额失败'
    )

    if (data !== null) {
      statCards.value[2].value = `¥${data.toLocaleString()}`
    }
  }

  /**
   * 获取统计卡片数据 - 平均返奖率
   */
  const fetchAverageReturnRate = async () => {
    const params = buildEnhancedParams()
    const data = await fetchData(
      getAverageReturnRate,
      params,
      '获取平均返奖率失败'
    )

    if (data !== null) {
      statCards.value[3].value = `${data}%`
    }
  }

  /**
   * 获取统计卡片数据 - 总提货金额
   */
  const fetchTotalClaimAmount = async () => {
    const params = buildEnhancedParams()
    const data = await fetchData(
      getTotalClaimAmount,
      params,
      '获取总提货金额失败'
    )

    if (data !== null) {
      statCards.value[5].value = `¥${data.toLocaleString()}`
    }
  }

  /**
   * 获取统计卡片数据 - 总充值电能
   */
  const fetchTotalRechargeAmount = async () => {
    const params = buildEnhancedParams()
    const data = await fetchData(
      getTotalRechargeAmount,
      params,
      '获取总充值电能失败'
    )

    if (data !== null) {
      statCards.value[6].value = `¥${data.toLocaleString()}`
    }
  }

  /**
   * 获取统计卡片数据 - 利润金额
   */
  const fetchProfitAmount = async () => {
    const params = buildEnhancedParams()
    const data = await fetchData(
      getProfitAmount,
      params,
      '获取利润金额失败'
    )

    if (data !== null) {
      statCards.value[7].value = `¥${data.toLocaleString()}`
    }
  }

  /**
   * 获取统计卡片数据 - 实际发货金额
   */
  const fetchTotalShippingCost = async () => {
    const params = buildEnhancedParams()
    const data = await fetchData(
      getTotalShippingCost,
      params,
      '获取实际发货金额失败'
    )

    if (data !== null) {
      statCards.value[8].value = `¥${data.toLocaleString()}`
    }
  }

  /**
   * 获取盲盒奖品价格统计数据
   */
  const fetchBoxPrizeStats = async () => {
    const params = buildEnhancedParams()
    const data = await fetchData(
      getBoxPrizeStats,
      params,
      '获取奖品价格统计数据失败'
    )

    if (data !== null) {
      // 修复：使用驼峰命名 totalPrizeValue 而不是 total_prize_value
      statCards.value[4].value = `¥${data.totalPrizeValue.toLocaleString()}`
    }
  }

  /**
   * 获取总充值笔数
   */
  const fetchTotalRechargeCount = async () => {
    const params = buildEnhancedParams()
    const data = await fetchData(
      getTotalRechargeCount,
      params,
      '获取总充值笔数失败'
    )

    if (data !== null) {
      statCards.value[9].value = data.toString()
    }
  }

  /**
   * 获取所有统计卡片数据（使用缓存和前端过滤）
   * @param {boolean} forceRefresh - 是否强制刷新缓存
   */
  const fetchDashboardStats = async (forceRefresh = false) => {
    loading.stats = true
    try {
      // 使用新的缓存机制获取统计数据
      await fetchStatisticsReport(forceRefresh)
    } catch (error) {
      console.error('获取统计数据失败:', error)
      ElMessage.error('获取统计数据失败')

      // 如果新接口调用失败，可以考虑降级到原有的多个API调用
      console.warn('统计报告接口调用失败，尝试使用原有接口...')
      try {
        await Promise.all([
          fetchTotalBoxOpenings(),
          fetchActiveUsers(),
          fetchTotalSales(),
          fetchAverageReturnRate(),
          fetchTotalClaimAmount(),
          fetchTotalRechargeAmount(),
          fetchProfitAmount(),
          fetchBoxPrizeStats(),
          fetchTotalShippingCost(),
          fetchTotalRechargeCount()
        ])
      } catch (fallbackError) {
        console.error('备用接口调用也失败:', fallbackError)
        ElMessage.error('获取统计数据失败，请稍后重试')
      }
    } finally {
      loading.stats = false
    }
  }

  /**
   * 获取开箱数据趋势
   * @param {Function} callback - 成功时的回调函数
   * @param {boolean} includeDate - 是否包含日期参数
   */
  const fetchBoxTrend = async (callback, includeDate = true) => {
    loading.trend = true
    try {
      // 构建参数对象
      const params = {
        timeType: timeRange.value,
        limit: 30
      }

      // 只有在需要时才添加日期参数（比如初始加载或日期范围变更时）
      if (includeDate) {
        // 使用参数构建方法，传递毫秒时间戳
        const enhancedParams = buildEnhancedParams()
        Object.assign(params, enhancedParams)
      }

      const response = await fetchData(
        getBoxTrend,
        params,
        '获取开箱趋势数据失败'
      )

      if (response && callback) {
        // 修复：处理后端返回的数据结构，将其转换为图表所需格式
        const timeLabels = []
        const openingCounts = []
        const salesAmounts = []

        // 检查响应是否为数组（直接返回的查询结果）
        if (Array.isArray(response)) {
          response.forEach(item => {
            timeLabels.push(item.time_label || '')
            openingCounts.push(item.opening_count || 0)
            salesAmounts.push(item.sales_amount || 0)
          })
        }

        // 构造图表所需的数据结构
        const chartData = {
          timeLabels,
          openingCounts,
          salesAmounts
        }
        
        callback(chartData)
      }
    } finally {
      loading.trend = false
    }
  }

  /**
   * 获取开箱结果分布
   * @param {Function} callback - 成功时的回调函数
   */
  const fetchBoxDistribution = async (callback) => {
    loading.distribution = true
    try {
      const response = await fetchData(
        getBoxDistribution, 
        dateParams.value, 
        '获取开箱分布数据失败'
      )
      
      if (response && callback) {
        // 修复：处理后端返回的数据结构，将其转换为图表所需格式
        const itemLevels = []
        const counts = []
        const percentages = []

        // 检查响应是否为数组（直接返回的查询结果）
        if (Array.isArray(response)) {
          response.forEach(item => {
            itemLevels.push(item.item_level || 0)
            counts.push(item.count || 0)
            percentages.push(item.percentage || 0)
          })
        }

        // 构造图表所需的数据结构
        const chartData = {
          itemLevels,
          counts,
          percentages
        }
        
        callback(chartData)
      }
    } finally {
      loading.distribution = false
    }
  }

  /**
   * 获取最新开箱记录
   */
  const fetchLatestBoxRecords = async () => {
    loading.latestRecords = true
    try {
      // 构建参数对象
      const params = {
        limit: 5,
        ...dateParams.value
      }

      const response = await fetchData(
        getLatestBoxRecords,
        params,
        '获取最新开箱记录失败'
      )

      if (response) {
        // 确保响应是数组
        const records = Array.isArray(response) ? response : [];
        
        // 修复：处理字段名称，确保与模板中使用的字段名一致
        latestRecords.value = records.map(item => ({
          username: `用户${item.uid || '未知'}`,
          avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
          boxName: item.boxName || item.boxname || '未知盲盒',
          prize: item.itemName || item.itemname || '未知物品',
          time: item.openTime || item.open_time || '未知时间',
          rarity: item.itemLevel || item.itemlevel || 0,
          orderId: item.oid || 0,
          price: item.price || 0,
          itemId: item.itemId || item.itemid || 0
        }))
      } else {
        // 如果没有数据，设置为空数组
        latestRecords.value = []
      }
    } catch (error) {
      console.error('获取最新开箱记录失败:', error)
      latestRecords.value = []
    } finally {
      loading.latestRecords = false
    }
  }

  /**
   * 获取热门盲盒排行
   */
  const fetchHotBoxes = async () => {
    loading.hotBoxes = true
    try {
      // 构建参数对象
      const params = {
        limit: 5,
        ...dateParams.value
      }

      const response = await fetchData(
        getHotBoxes,
        params,
        '获取热门盲盒排行失败'
      )

      if (response) {
        // 确保响应是数组
        const boxData = Array.isArray(response) ? response : [];
        
        // 找出最大销量，用于计算热度百分比
        const maxSaleVolume = boxData.length > 0 ? 
          Math.max(...boxData.map(item => Number(item.sale_volume || item.saleVolume || 0))) : 1;
          
        // 修复：处理字段名称，确保与模板中使用的字段名一致
        topBoxes.value = boxData.map((item) => {
          // 获取销量，优先使用saleVolume，如果不存在则使用sale_volume
          const saleVolume = Number(item.saleVolume || item.sale_volume || 0);
          // 计算热度百分比
          const popularity = Math.min(100, Math.round((saleVolume / maxSaleVolume) * 100)) || 0;

          return {
            id: item.id || 0,
            name: item.name || '未知盲盒',
            image: item.imageBox || item.image_box || 'https://via.placeholder.com/60',
            saleVolume: saleVolume,
            price: Number(item.price || 0).toFixed(2),
            returnRate: Number(item.returnRate || item.return_rate || 0).toFixed(2),
            popularity: popularity
          }
        })
      } else {
        // 如果没有数据，设置为空数组
        topBoxes.value = []
      }
    } catch (error) {
      console.error('获取热门盲盒排行失败:', error)
      topBoxes.value = []
    } finally {
      loading.hotBoxes = false
    }
  }

  /**
   * 加载所有数据
   */
  const loadAllData = async () => {
    await fetchDashboardStats()
    return {
      fetchBoxTrend,
      fetchBoxDistribution,
      fetchLatestBoxRecords,
      fetchHotBoxes
    }
  }

  /**
   * 主动获取最新统计数据用于导出（不使用缓存）
   * @param {string} startTime - 开始时间
   * @param {string} endTime - 结束时间
   * @param {string} streamerFilter - 主播筛选条件（仅用于记录，实际导出所有数据）
   * @returns {Promise<Array>} 统计数据数组
   */
  const fetchLatestStatisticsForExport = async (startTime, endTime, streamerFilterValue) => {
    try {
      // 临时设置导出专用的时间范围
      const originalDateTimeRange = dateTimeRange ? dateTimeRange.value : null

      // 设置导出专用的时间范围
      if (dateTimeRange) {
        dateTimeRange.value = [startTime, endTime]
      }

      // 使用与Dashboard统计面板相同的参数构建逻辑，但强制获取所有数据
      const params = buildEnhancedParams()
      
      // 确保导出时不受筛选条件影响，总是获取完整数据
      delete params.streamerFilter 

      console.log('导出功能：使用统一参数构建', {
        原始时间: { startTime, endTime },
        构建的参数: params,
        筛选条件说明: '导出时忽略筛选条件，获取所有数据'
      })

      // 调用统计报告API - 获取完整数据
      const data = await fetchData(
        getStatisticsReport,
        params,
        '导出功能：获取统计报告失败'
      )

      // 恢复原始参数
      if (dateTimeRange) {
        dateTimeRange.value = originalDateTimeRange
      }

      console.log('导出功能：API响应数据', data)

      if (data && Array.isArray(data)) {
        console.log('导出功能：成功获取统计数据', data.length, '项')
        return data
      } else {
        console.warn('导出功能：统计数据格式不正确', data)
        return []
      }
    } catch (error) {
      console.error('导出功能：获取统计数据失败', error)
      throw error
    }
  }

  /**
   * 按分类分组统计数据
   * @param {Array} statisticsData - 统计数据数组
   * @returns {Object} 按分类分组的数据对象
   */
  const groupStatisticsByCategory = (statisticsData) => {
    const grouped = {}

    statisticsData.forEach(item => {
      const category = item.category || '其他统计'

      if (!grouped[category]) {
        grouped[category] = []
      }

      grouped[category].push(item)
    })

    // 按分类名称排序
    const sortedGrouped = {}
    Object.keys(grouped).sort().forEach(key => {
      sortedGrouped[key] = grouped[key]
    })

    return sortedGrouped
  }

  return {
    loading,
    statCards,
    latestRecords,
    topBoxes,
    statisticsCache,
    fetchDashboardStats,
    fetchBoxTrend,
    fetchBoxDistribution,
    fetchLatestBoxRecords,
    fetchHotBoxes,
    loadAllData,

    // 新的缓存和过滤方法
    fetchStatisticsReport,
    refreshStatisticsWithFilter,

    // 导出相关方法
    fetchLatestStatisticsForExport,
    groupStatisticsByCategory
  }
}