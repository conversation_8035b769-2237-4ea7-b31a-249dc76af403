/**
 * 日期参数处理组合式函数
 * 提供日期范围处理和参数生成的通用方法
 */
import { ref, computed } from 'vue'

/**
 * 日期参数处理组合式函数
 * @returns {Object} 日期相关的状态和方法
 */
export function useDateParams() {
  // 初始化日期范围为当天
  const today = new Date()
  const formattedToday = formatDate(today)
  const dateRange = ref([formattedToday, formattedToday])

  /**
   * 获取当前日期范围的参数对象
   * @returns {Object} 包含 startDate 和 endDate 的参数对象
   */
  const dateParams = computed(() => {
    if (!dateRange.value || dateRange.value.length !== 2) {
      return {}
    }
    return {
      startDate: dateRange.value[0],
      endDate: dateRange.value[1]
    }
  })

  /**
   * 获取昨天的日期参数对象
   * @returns {Object} 包含昨天日期的参数对象
   */
  const getYesterdayParams = () => {
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    const yesterdayFormatted = formatDate(yesterday)
    
    return {
      startDate: yesterdayFormatted,
      endDate: yesterdayFormatted
    }
  }

  /**
   * 格式化日期为 YYYY-MM-DD 格式
   * @param {Date} date - 日期对象
   * @returns {string} 格式化后的日期字符串
   */
  function formatDate(date) {
    return date.toISOString().split('T')[0] // 格式：YYYY-MM-DD
  }

  /**
   * 计算趋势百分比变化
   * @param {number} currentValue - 当前值
   * @param {number} previousValue - 前一个值
   * @returns {number} 百分比变化
   */
  const calculateTrendPercentage = (currentValue, previousValue) => {
    // 如果前一个值为0，则无法计算百分比变化，返回0
    if (previousValue === 0) return 0
    
    // 计算百分比变化
    const change = ((currentValue - previousValue) / previousValue) * 100
    
    // 四舍五入到一位小数
    return Math.round(change * 10) / 10
  }

  return {
    dateRange,
    dateParams,
    getYesterdayParams,
    calculateTrendPercentage
  }
} 