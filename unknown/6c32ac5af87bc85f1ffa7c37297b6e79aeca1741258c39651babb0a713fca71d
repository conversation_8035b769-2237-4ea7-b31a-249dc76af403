# VIM用户批量创建工具 - 快速使用指南

## 快速开始

### 1. 直接运行测试
启动VIM-Admin-Server项目，工具会自动在主方法中执行批量创建30个测试用户。

```bash
# 启动项目
mvn spring-boot:run
# 或者
java -jar VIM-Admin-Server.jar
```

### 2. 修改创建数量
编辑 `RuoYiApplication.java` 文件中的 `createCount` 变量：

```java
// 修改这个数值来改变创建的用户数量（1-199）
int createCount = 30;  // 改为你想要的数量
```

### 3. 禁用测试功能
正式环境部署时，注释掉测试代码：

```java
// 注释掉这行来禁用测试
// testBatchCreateUsers(context);
```

## 生成的测试数据示例

### 用户基本信息
```
ID: 1-30 (连续分配)
昵称: 星空酱1234, 月影君5678, 樱花殿9012 (二次元风格)
手机号: 10000000001, 10000000002, 10000000003 (基于ID生成)
用户名: user1, user2, user3
密码: 123456 (已加密)
头像: 随机选择8个头像之一
```

### 默认设置
```
货币: 0.00
钥匙: 0.00
身份: 1 (普通用户)
状态: 1 (正常)
实名认证: 0 (未实名)
经验: 0.00
等级: 0
邀请码: VIM000001, VIM000002, VIM000003
```

## 控制台输出示例

```
=== 开始测试批量创建VIM用户功能 ===
准备创建 30 个测试用户...
2025-01-03 10:30:15.123 INFO  开始批量创建30个VIM测试用户
2025-01-03 10:30:15.125 INFO  测试用户ID将从1开始创建
2025-01-03 10:30:15.234 INFO  成功创建用户 [1/30]: 星空酱1234 (ID: 1)
2025-01-03 10:30:15.245 INFO  成功创建用户 [2/30]: 月影君5678 (ID: 2)
...

=== 批量创建结果 ===
总数: 30
成功: 30
失败: 0
耗时: 1250ms

成功创建的用户昵称:
  - 星空酱1234
  - 月影君5678
  - 樱花殿9012
  ...

=== 测试完成 ===
注意：测试用户ID范围为1-199，正式用户ID从200开始
```

## 重要说明

### ⚠️ 数据安全
- **测试用户ID**: 1-199
- **正式用户ID**: 200+
- 不会影响现有正式用户数据

### ⚠️ 使用限制
- 最多创建199个测试用户
- 如果ID范围已满，会提示错误
- 手机号和昵称必须唯一

### ⚠️ 清理数据
如需清理测试数据，执行SQL：
```sql
DELETE FROM vim_user WHERE id BETWEEN 1 AND 199;
```

## 常见问题

### Q: 创建失败怎么办？
A: 检查控制台错误信息，常见原因：
- 数据库连接问题
- ID范围已满
- 唯一性约束冲突

### Q: 如何修改昵称风格？
A: 编辑 `VimUserBatchCreateUtil.java` 中的 `ANIME_PREFIXES` 和 `ANIME_SUFFIXES` 数组

### Q: 如何添加更多头像？
A: 编辑 `VimUserBatchCreateUtil.java` 中的 `AVATAR_URLS` 数组

### Q: 正式环境如何禁用？
A: 注释掉 `RuoYiApplication.java` 中的 `testBatchCreateUsers(context);` 调用

## 文件位置

- **工具类**: `src/main/java/com/ruoyi/common/utils/VimUserBatchCreateUtil.java`
- **主方法**: `src/main/java/com/ruoyi/RuoYiApplication.java`
- **数据库映射**: `src/main/resources/mybatis/VimUserSys/VimUserMapper.xml`

## 技术特点

✅ **智能ID分配**: 自动查找可用的连续ID范围
✅ **事务安全**: 失败自动回滚，不会产生脏数据
✅ **详细日志**: 实时显示创建进度和结果
✅ **唯一性保证**: 自动处理重复数据问题
✅ **二次元风格**: 生成有趣的测试用户昵称
✅ **零配置**: 启动即可使用，无需额外配置
